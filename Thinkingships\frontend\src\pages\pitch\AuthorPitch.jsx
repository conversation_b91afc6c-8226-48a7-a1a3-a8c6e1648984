import { useState } from 'react';

const AuthorPitch = () => {
  const [activeTab, setActiveTab] = useState('Browse Request');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showDetailsView, setShowDetailsView] = useState(false);
  const [showStoryView, setShowStoryView] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showStoryModal, setShowStoryModal] = useState(false);
  const [selectedPitch, setSelectedPitch] = useState(null);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [filters, setFilters] = useState({
    contentType: 'All',
    genre: 'All',
    language: 'All',
    payout: 'All',
    deadline: 'All'
  });
  const [formData, setFormData] = useState({
    title: '',
    brief: '',
    contentType: '',
    genre: '',
    deadline: '',
    wordCount: '',
    language: '',
    tags: '',
    guidelines: '',
    requireFirstRights: false,
    showSubmissionCount: false,
    autoCloseDeadline: false
  });

  const browseRequests = [
    {
      id: 1,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Urban Life',
      type: 'Blog',
      payout: '₹500',
      status: 'Open',
      deadline: 'July 19, 2025',
      description: 'Share A Compelling Article About Life In An Indian Metro City — The Chaos, The Quiet Moments, The Everyday Characters, And The Ever-Changing Pace.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Echoes Of The Forgotten',
      publication: 'Inkspire Magazine',
      genre: 'Romance',
      type: 'Story',
      payout: '₹750',
      status: 'Open',
      deadline: 'July 19, 2025',
      description: 'A Haunting Narrative About People, Places, Or Memories That Time Has Left Behind. We\'re Looking For Stories That Blend Nostalgia With Quiet Revelation.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Midnight Chronicles',
      publication: 'Inkspire Magazine',
      genre: 'Fantasy',
      type: 'Story',
      payout: '₹600',
      status: 'Open',
      deadline: 'Jul 10, 2025',
      description: 'Create an epic fantasy tale with Indian mythology elements. Include magical realism and cultural authenticity.',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    }
  ];

  const myPitches = [
    {
      id: 1,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Urban Life',
      type: 'Blog',
      status: 'Accepted',
      deadline: 'May 29, 2025',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Romance',
      type: 'Story',
      status: 'Pending',
      deadline: 'Jul 14, 2025',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Fantasy',
      type: 'Poem',
      status: 'Rejected',
      deadline: 'Mar 23, 2025',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    },
    {
      id: 4,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Science Fiction',
      type: 'e-book',
      status: 'Pending',
      deadline: 'Dec 29, 2024',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg'
    }
  ];

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCloseForm = () => {
    setShowCreateForm(false);
    setFormData({
      title: '',
      brief: '',
      contentType: '',
      genre: '',
      deadline: '',
      wordCount: '',
      language: '',
      tags: '',
      guidelines: '',
      requireFirstRights: false,
      showSubmissionCount: false,
      autoCloseDeadline: false
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission here
    handleCloseForm();
  };

  const currentData = activeTab === 'Browse Request' ? browseRequests : myPitches;

  const filteredPitches = currentData.filter(pitch => {
    if (filters.contentType !== 'All' && pitch.type !== filters.contentType) return false;
    if (filters.genre !== 'All' && pitch.genre !== filters.genre) return false;
    if (filters.language !== 'All' && pitch.language !== filters.language) return false;
    if (filters.payout !== 'All' && pitch.payout !== filters.payout) return false;
    if (filters.deadline !== 'All' && pitch.deadline !== filters.deadline) return false;
    return true;
  });

  // Sample submissions data for the modal
  const sampleSubmissions = [
    {
      id: 1,
      author: 'boho_beauty',
      title: "India's Oldest Temple",
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
      story: `On days when work wears you down, I wish you little joys to embrace on your lap. On days when relationships tear you down, I wish you grace to fill the gap. For life isn't always fair and dandy, Some nights are longer than days combined. Sometimes you must pretend to be strong Until pretension manoeuvres the masquerade. I wish you infectious laughter and mirth, I wish for you little moments to redefine the day. I wish for you integrity to stand tall amidst storms, I wish amidst a crowd of maturity, there's a naive child's play.

For life is a road not uphill forever, The crests and troughs and a roller coaster ride. May you find the beauty and the mundaneness of everyday, May your friends add and worries divide. Its seldom easy to bask in the sea of truth, For truth often comes with its own implications, May you feel have to strength to never halt. May you have the yellow of sunshine, The peace of the rustling leaves, the limitlessness of the sky. May your wings devour the wide horizon And keep at it, never tire.

On days it gets difficult to feel positive, I wish you light and love and warmth. On days negativity wears heavy on you, I wish the rain pours on your chaotic swarm. May you be blessed with the lightness of air, May you know death in wrinkles and warm. May love lead your life to solace, May all your good deeds outdo all harm.

For life is a road not uphill forever, The crests and troughs and a roller coaster ride.

On days when work wears you down, I wish you little joys to embrace on your lap. On days when relationships tear you down, I wish you grace to fill the gap.`
    },
    {
      id: 2,
      author: 'curious_kat',
      title: "Ahmedabad's Rutheesing Jain Temple",
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
      story: `The Hutheesing Jain Temple, also known as the Rutheesing Jain Temple, stands as one of the most magnificent examples of Jain architecture in Ahmedabad. Built in 1848 by Sheth Hutheesing, a wealthy Jain merchant, this temple is dedicated to Dharmanatha, the 15th Jain Tirthankara.

The temple's architecture is a stunning blend of traditional Jain design with intricate carvings that tell stories of devotion and spirituality. The main structure rises majestically with its ornate spires reaching towards the heavens, symbolizing the soul's journey towards liberation.

What makes this temple truly special is its detailed stone work. Every pillar, every arch, and every surface is adorned with delicate carvings depicting various Jain symbols, celestial beings, and geometric patterns. The craftsmanship is so fine that it seems almost impossible that human hands could have created such intricate beauty.

The temple complex includes multiple shrines, each housing beautiful marble idols of Jain Tirthankaras. The main shrine contains a stunning idol of Dharmanatha, carved from white marble and adorned with precious stones during special ceremonies.

Visitors often speak of the peaceful atmosphere that pervades the temple grounds. The morning prayers, the gentle chanting, and the soft footsteps of devotees create a symphony of spirituality that touches the heart of every visitor, regardless of their faith.`
    },
    {
      id: 3,
      author: 'wildwanderer',
      title: 'Tales of Tughlaqabad',
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      story: `Tughlaqabad stands today as a haunting reminder of ambitious dreams and their inevitable decay. Built in the 14th century by Ghiyas-ud-din Tughlaq, this fortified city was meant to be the new capital of the Delhi Sultanate, a symbol of power and permanence.

The massive walls of Tughlaqabad stretch for miles, built with a unique technique using large stone blocks without mortar. These walls, some reaching heights of 15 meters, were designed to withstand any siege. The city was planned with meticulous detail - residential areas, markets, mosques, and palaces, all within the protective embrace of these formidable fortifications.

But Tughlaqabad's glory was short-lived. Legend speaks of a curse placed by the Sufi saint Nizamuddin Auliya, who declared that the city would remain uninhabited. Whether by curse or circumstance, the city was abandoned within a few years of its completion, leaving behind only ruins that whisper tales of its brief but magnificent past.

Walking through Tughlaqabad today, one can almost hear the echoes of bustling markets, the calls of merchants, and the footsteps of courtiers. The ruins speak of a time when this was a thriving metropolis, when these walls protected thousands of inhabitants, when these gates welcomed travelers from distant lands.

The tomb of Ghiyas-ud-din Tughlaq stands on an island in the middle of what was once a lake, connected to the main city by a causeway. This tomb, with its sloping walls and distinctive architecture, represents the final resting place of a ruler whose dreams were larger than life itself.`
    },
    {
      id: 4,
      author: 'coffee_lover',
      title: 'The Saga of the Komagata Maru',
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
      story: `The year was 1914, and the steamship Komagata Maru carried within its hull not just 376 passengers, but the hopes and dreams of an entire community seeking a better life. This vessel, chartered by Gurdit Singh, a Sikh entrepreneur, was destined to become a symbol of resistance against discriminatory immigration policies.

The passengers aboard were primarily Sikhs, with some Muslims and Hindus, all British subjects from Punjab seeking to immigrate to Canada. They believed their status as British subjects would grant them entry into any part of the British Empire. However, they were about to encounter the harsh reality of racial discrimination disguised as immigration policy.

When the Komagata Maru arrived in Vancouver's Burrard Inlet on May 23, 1914, it was met not with welcome, but with hostility. The Canadian government, under pressure from white supremacist groups, had implemented the Continuous Journey Regulation, which effectively barred Indian immigration by requiring immigrants to travel directly from their country of origin - an impossible feat given the lack of direct shipping routes.

For two months, the ship remained anchored in the harbor, its passengers trapped between the sea and an unwelcoming shore. The local Sikh community rallied to support their stranded compatriots, providing food and legal assistance. Court battles ensued, with lawyers arguing that as British subjects, these passengers had every right to enter Canada.

The conditions aboard the ship deteriorated rapidly. Food and water supplies dwindled, sanitation became a serious concern, and the psychological toll on the passengers grew heavier with each passing day. Children fell ill, elderly passengers struggled with the confined conditions, and hope began to fade.

On July 23, 1914, after a prolonged legal battle and mounting international pressure, the Canadian government ordered the ship to leave. Only 24 passengers were allowed to remain - those who had previously lived in Canada. The rest were forced to return to India, their dreams shattered but their dignity intact.

The return journey was fraught with tragedy. Upon reaching Calcutta, British authorities, suspicious of the passengers' potential revolutionary activities, attempted to transport them directly to Punjab by train. The passengers, having endured months of humiliation, refused to board. In the ensuing confrontation at Budge Budge, police opened fire, killing 19 passengers and injuring many more.

The Komagata Maru incident became a catalyst for the Indian independence movement. It exposed the hypocrisy of British claims of equality within the Empire and galvanized Indian nationalism. The passengers, who had set out seeking economic opportunities, returned as symbols of resistance against colonial oppression.

Today, the story of the Komagata Maru serves as a powerful reminder of the struggles faced by immigrants and the importance of fighting against discrimination in all its forms. It stands as a testament to the courage of those who dare to dream of a better life, even in the face of seemingly insurmountable obstacles.`
    },
    {
      id: 5,
      author: 'quirkymind',
      title: 'The Story of the Bahmanis',
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      story: `In the heart of the Deccan plateau, where the red earth meets the azure sky, once stood the mighty Bahmani Sultanate, a kingdom that would reshape the political landscape of medieval India. Founded in 1347 by Alauddin Hasan Bahman Shah, this sultanate emerged from the ashes of rebellion against the Delhi Sultanate, marking the beginning of a new era in South Indian history.

The story begins with Zafar Khan, a Turkish noble who served under Muhammad bin Tughluq. Disillusioned with the erratic policies of the Delhi Sultan and inspired by the desire for independence, he led a successful revolt in the Deccan. Upon establishing his kingdom, he took the title of Alauddin Hasan Bahman Shah, naming his dynasty after his spiritual guide, Hazrat Bahman Baba.

The Bahmani capital of Gulbarga became a beacon of culture and learning. The sultans were great patrons of art, literature, and architecture. They invited scholars, poets, and artisans from Persia, Central Asia, and other parts of India, creating a cosmopolitan atmosphere that fostered intellectual growth and cultural synthesis.

The kingdom's military prowess was legendary. The Bahmanis maintained a formidable army that included war elephants, cavalry, and infantry units. Their strategic location allowed them to control important trade routes, bringing immense wealth that funded their military campaigns and architectural projects.

One of the most remarkable aspects of Bahmani rule was their administrative system. They divided their kingdom into four provinces, each governed by a capable administrator. This decentralized approach allowed for efficient governance while maintaining central authority. The sultans also implemented a unique policy of religious tolerance, employing both Muslims and Hindus in important administrative positions.

The architectural legacy of the Bahmanis is breathtaking. The Jama Masjid in Gulbarga, with its unique design featuring no open courtyard, stands as a testament to their innovative approach to Islamic architecture. The tomb of Ahmad Shah I, with its magnificent dome and intricate calligraphy, showcases the artistic heights achieved during their reign.

However, like many great empires, the Bahmani Sultanate faced internal strife. The rivalry between the Deccanis (local Muslims) and the Afaqis (foreign Muslims) created persistent tensions. These factional disputes weakened the central authority and eventually led to the fragmentation of the kingdom.

The later period saw the capital shift to Bidar, where the sultans built magnificent palaces and gardens. The Bidar Fort, with its impressive fortifications and beautiful palaces, represents the architectural zenith of Bahmani power. The intricate tile work, the sophisticated water management systems, and the harmonious blend of Persian and local architectural elements make it a masterpiece of medieval Indian architecture.

The Bahmani Sultanate's influence extended far beyond its political boundaries. It served as a bridge between North and South India, facilitating cultural exchange and trade. The Persian language flourished under their patronage, and many important literary works were produced during this period.

The kingdom's decline began in the late 15th century when powerful nobles started asserting their independence. By 1518, the Bahmani Sultanate had fragmented into five successor states: Bijapur, Golconda, Ahmednagar, Berar, and Bidar. These kingdoms, known as the Deccan Sultanates, would continue the Bahmani legacy for several more centuries.

The story of the Bahmanis is one of ambition, cultural synthesis, and eventual fragmentation. It reminds us that even the mightiest empires are subject to the inexorable forces of time and change. Yet their contributions to Indian culture, architecture, and administration continue to inspire and educate us about our rich and diverse heritage.

Today, as we walk through the ruins of Gulbarga and Bidar, we can almost hear the echoes of a glorious past - the sound of horses' hooves on cobblestone streets, the calls of merchants in bustling bazaars, and the melodious recitation of poetry in royal courts. The Bahmani Sultanate may have ended, but its legacy lives on in the stones of its monuments and the pages of history.`
    }
  ];

  const handleViewDetails = (pitch) => {
    setSelectedPitch(pitch);
    setShowDetailsView(true);
    setShowCreateForm(false);
    setShowStoryView(false);
  };

  const handleViewStory = (submission) => {
    setSelectedSubmission(submission);
    setShowStoryView(true);
    setShowDetailsView(false);
    setShowStoryModal(true);
  };

  const closeDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedPitch(null);
  };

  const closeStoryModal = () => {
    setShowStoryModal(false);
    setSelectedSubmission(null);
  };

  const handleBackToList = () => {
    setShowDetailsView(false);
    setShowStoryView(false);
    setSelectedPitch(null);
    setSelectedSubmission(null);
  };

  const handleBackToDetails = () => {
    setShowStoryView(false);
    setSelectedSubmission(null);
  };

  return (
    <div className="p-6 bg-gradient-to-br from-slate-50 to-slate-200 min-h-screen relative overflow-y-auto overflow-x-hidden opacity-0" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
      {/* Header, Tabs and Filters - Hide when create form, details view, or story view is shown */}
      {!showCreateForm && !showDetailsView && !showStoryView && (
        <>
          {/* Header */}
          <div className="mb-8 relative z-10 opacity-0 -translate-y-4" style={{ animation: 'slideInUp 0.6s ease-out 0.1s forwards' }}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent relative">
              Pitch Hub
              <div className="absolute -bottom-2 left-0 w-15 h-1 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full"></div>
            </h1>
          </div>

          {/* Tabs */}
          <div className="flex gap-2 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.2s forwards' }}>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'Browse Request'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Browse Request')}
            >
              Browse Request
            </button>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'My Pitches'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('My Pitches')}
            >
              My Pitches
            </button>
          </div>

          {/* Subtitle - Only show for My Pitches tab */}
          {activeTab === 'My Pitches' && (
            <p className="text-gray-600 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.3s forwards' }}>
              Track All Your Submitted Pitches To Publications And Businesses
            </p>
          )}

          {/* Filters */}
          <div className="flex gap-3 mb-8 opacity-0 translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.4s forwards' }}>
            <select
              value={filters.contentType}
              onChange={(e) => handleFilterChange('contentType', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Content Type</option>
              <option value="Blog">Blog</option>
              <option value="Story">Story</option>
              <option value="Article">Article</option>
            </select>

            <select
              value={filters.genre}
              onChange={(e) => handleFilterChange('genre', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Genre</option>
              <option value="Romance">Romance</option>
              <option value="Fantasy">Fantasy</option>
              <option value="Urban Life">Urban Life</option>
              <option value="Science Fiction">Science Fiction</option>
              <option value="Environmental">Environmental</option>
            </select>

            <select
              value={filters.language}
              onChange={(e) => handleFilterChange('language', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Language</option>
              <option value="English">English</option>
              <option value="Hindi">Hindi</option>
              <option value="Bengali">Bengali</option>
            </select>

            <select
              value={filters.payout}
              onChange={(e) => handleFilterChange('payout', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Payout</option>
              <option value="₹400">₹400</option>
              <option value="₹500">₹500</option>
              <option value="₹600">₹600</option>
              <option value="₹750">₹750</option>
            </select>

            <select
              value={filters.deadline}
              onChange={(e) => handleFilterChange('deadline', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Deadline</option>
              <option value="May 29, 2025">May 29, 2025</option>
              <option value="Jun 15, 2025">Jun 15, 2025</option>
              <option value="Jul 10, 2025">Jul 10, 2025</option>
              <option value="Jul 14, 2025">Jul 14, 2025</option>
              <option value="Mar 23, 2025">Mar 23, 2025</option>
              <option value="Dec 29, 2024">Dec 29, 2024</option>
            </select>
          </div>
        </>
      )}

      {/* Main Content Area */}
      {showDetailsView ? (
        /* Details View */
        <div className="opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          {/* Back Button */}
          <div className="mb-6">
            <button
              onClick={handleBackToList}
              className="flex items-center gap-2 px-4 py-2 bg-white/70 hover:bg-white/90 rounded-lg transition-all duration-300 text-gray-700 hover:text-gray-900"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              Back to Pitches
            </button>
          </div>

          {/* Details Content */}
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-200 overflow-hidden">
            {/* Header */}
            <div className="p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {selectedPitch?.title}
              </h2>
              <p className="text-gray-600 mt-1">Submissions Overview</p>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Pitch Info */}
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch?.genre}</div>
                    <div className="text-sm text-gray-600">Genre</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch?.type}</div>
                    <div className="text-sm text-gray-600">Type</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch?.payout}</div>
                    <div className="text-sm text-gray-600">Payout</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{sampleSubmissions.length}</div>
                    <div className="text-sm text-gray-600">Total Submissions</div>
                  </div>
                </div>
              </div>

              {/* Submissions Table */}
              <div className="bg-white/95 backdrop-blur-xl rounded-2xl border border-blue-200 shadow-blue-100 overflow-hidden">
                {/* Table Header */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
                  <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                    <div className="col-span-3">Author</div>
                    <div className="col-span-3">Title</div>
                    <div className="col-span-2">Submitted On</div>
                    <div className="col-span-2">Status</div>
                    <div className="col-span-2">Action</div>
                  </div>
                </div>

                {/* Table Body */}
                <div className="divide-y divide-gray-100">
                  {sampleSubmissions.map((submission) => (
                    <div
                      key={submission.id}
                      className="px-6 py-4 hover:bg-blue-50/50 transition-all duration-300"
                    >
                      <div className="grid grid-cols-12 gap-4 items-center">
                        {/* Author */}
                        <div className="col-span-3 flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-blue-200 flex-shrink-0">
                            <img
                              src={submission.avatar}
                              alt="Author"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-800">{submission.author}</p>
                          </div>
                        </div>

                        {/* Title */}
                        <div className="col-span-3">
                          <p className="font-medium text-gray-800 truncate">{submission.title}</p>
                        </div>

                        {/* Submitted On */}
                        <div className="col-span-2">
                          <span className="text-gray-700">{submission.submittedOn}</span>
                        </div>

                        {/* Status */}
                        <div className="col-span-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                            submission.status === 'Accepted'
                              ? 'bg-green-100 text-green-700'
                              : submission.status === 'Pending'
                              ? 'bg-yellow-100 text-yellow-700'
                              : submission.status === 'Rejected'
                              ? 'bg-red-100 text-red-700'
                              : 'bg-blue-100 text-blue-700'
                          }`}>
                            {submission.status}
                          </span>
                        </div>

                        {/* Action */}
                        <div className="col-span-2">
                          <button
                            onClick={() => handleViewStory(submission)}
                            className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-0.5"
                          >
                            View
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : showStoryView ? (
        /* Story View */
        <div className="opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          {/* Back Button */}
          <div className="mb-6">
            <button
              onClick={handleBackToDetails}
              className="flex items-center gap-2 px-4 py-2 bg-white/70 hover:bg-white/90 rounded-lg transition-all duration-300 text-gray-700 hover:text-gray-900"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              Back to Submissions
            </button>
          </div>

          {/* Story Content */}
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-200 overflow-hidden">
            {/* Story Header */}
            <div className="p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center gap-4">
                <img
                  src={selectedSubmission?.avatar}
                  alt="Author"
                  className="w-12 h-12 rounded-full border-2 border-blue-200"
                />
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{selectedSubmission?.title}</h2>
                  <p className="text-blue-600">by {selectedSubmission?.author}</p>
                </div>
              </div>
            </div>

            {/* Story Content */}
            <div className="p-6">
              <div className="bg-gradient-to-br from-blue-50/50 to-purple-50/50 rounded-2xl p-8 mb-6">
                <div className="prose prose-lg max-w-none">
                  <div className="text-gray-800 leading-relaxed whitespace-pre-line">
                    {selectedSubmission?.story}
                  </div>
                </div>
              </div>

              {/* Story Metadata */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white/80 rounded-xl p-4 text-center border border-blue-100">
                  <div className="text-sm text-gray-600">Content Type</div>
                  <div className="font-semibold text-blue-600">Short Story</div>
                </div>
                <div className="bg-white/80 rounded-xl p-4 text-center border border-blue-100">
                  <div className="text-sm text-gray-600">Language</div>
                  <div className="font-semibold text-blue-600">English</div>
                </div>
                <div className="bg-white/80 rounded-xl p-4 text-center border border-blue-100">
                  <div className="text-sm text-gray-600">Word Count</div>
                  <div className="font-semibold text-blue-600">{selectedSubmission?.story.split(' ').length} Words</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : !showCreateForm && (
        /* Pitch List */
        <div className="space-y-6">
          {activeTab === 'Browse Request' ? (
            // Card format for Browse Request (same as Pitch page)
            filteredPitches.map((pitch, index) => (
              <div
                key={pitch.id}
                className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 border border-blue-100 shadow-blue-100 hover:border-blue-200 hover:shadow-blue-200 transition-all duration-300 relative overflow-hidden opacity-0 translate-y-8 hover:scale-105 group"
                style={{
                  animationDelay: `${index * 0.1}s`,
                  animation: 'slideInUp 0.6s ease-out forwards',
                  boxShadow: '0 8px 32px rgba(59, 130, 246, 0.08)'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-800"></div>
                <div className="flex justify-between items-start mb-5 relative z-10">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-800 mb-3 leading-tight">{pitch.title}</h3>
                    <div className="flex items-center gap-3">
                      <img
                        src={pitch.avatar}
                        alt={pitch.publication}
                        className="w-10 h-10 rounded-full border-2 border-blue-200 transition-all duration-300 hover:scale-110 hover:border-blue-500"
                      />
                      <span className="font-semibold text-gray-700">{pitch.publication}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-5 relative z-10">
                  <div className="flex gap-8 mb-3">
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Genre:</span>
                      <span className="text-gray-800 font-medium">{pitch.genre}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Type:</span>
                      <span className="text-gray-800 font-medium">{pitch.type}</span>
                    </div>
                  </div>
                  <div className="flex gap-8">
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Payout:</span>
                      <span className="text-gray-800 font-medium">{pitch.payout}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Deadline:</span>
                      <span className="text-gray-800 font-medium">{pitch.deadline}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-6 relative z-10">
                  <p className="text-gray-700 leading-relaxed text-sm">{pitch.description}</p>
                </div>

                <div className="flex justify-end relative z-10">
                  <button
                    onClick={() => handleViewDetails(pitch)}
                    className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden group"
                  >
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                    View Details
                  </button>
                </div>
              </div>
            ))
          ) : (
            // Table format for My Pitches
            <div className="bg-white/95 backdrop-blur-xl rounded-2xl border border-blue-200 shadow-blue-100 overflow-hidden opacity-0 translate-y-4" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
              {/* Table Header */}
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                  <div className="col-span-4">Publication</div>
                  <div className="col-span-2">Type</div>
                  <div className="col-span-2">Status</div>
                  <div className="col-span-2">Deadline</div>
                  <div className="col-span-2">Action</div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-100">
                {filteredPitches.map((pitch, index) => (
                  <div
                    key={pitch.id}
                    className="px-6 py-4 hover:bg-blue-50/50 transition-all duration-300 opacity-0 translate-y-4"
                    style={{
                      animationDelay: `${index * 0.1}s`,
                      animation: 'slideInUp 0.6s ease-out forwards'
                    }}
                  >
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Publication */}
                      <div className="col-span-4 flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-blue-200 flex-shrink-0">
                          <img
                            src={pitch.avatar}
                            alt="Publication"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800">{pitch.title}</h3>
                          <p className="text-sm text-blue-600">{pitch.publication}</p>
                        </div>
                      </div>

                      {/* Type */}
                      <div className="col-span-2">
                        <span className="text-gray-700">{pitch.type}</span>
                      </div>

                      {/* Status */}
                      <div className="col-span-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          pitch.status === 'Accepted'
                            ? 'bg-green-100 text-green-700'
                            : pitch.status === 'Pending'
                            ? 'bg-yellow-100 text-yellow-700'
                            : pitch.status === 'Rejected'
                            ? 'bg-red-100 text-red-700'
                            : 'bg-blue-100 text-blue-700'
                        }`}>
                          {pitch.status}
                        </span>
                      </div>

                      {/* Deadline */}
                      <div className="col-span-2">
                        <span className="text-gray-700">{pitch.deadline}</span>
                      </div>

                      {/* Action */}
                      <div className="col-span-2">
                        <button className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-0.5">
                          Action
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Create Form - Hidden by default since Create New button is removed */}
      {showCreateForm && (
        <div className="animate-slideInUp">
          <div className="max-w-4xl mx-auto bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-blue-200 overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Create Submission Guidelines</h2>
              <button className="p-2 hover:bg-blue-100 rounded-lg transition-colors duration-200" onClick={handleCloseForm}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-slate-600">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            <div className="p-6 text-slate-600 bg-blue-50/50">
              Set up your submission requirements to invite Authors to share their best work. Define what you're looking for and the qualifications Authors need to meet.
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Title and Brief */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Title</label>
                  <div className="text-sm text-slate-500 mb-2">Give your submission guidelines a clear, descriptive title</div>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="e.g., 'Short Stories for Urban Life Magazine'"
                    value={formData.title}
                    onChange={(e) => handleFormChange('title', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Brief</label>
                  <div className="text-sm text-slate-500 mb-2">Provide a short summary of what you're looking for</div>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="Brief description of your requirements"
                    value={formData.brief}
                    onChange={(e) => handleFormChange('brief', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Content Type and Genre */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Content Type</label>
                  <select
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.contentType}
                    onChange={(e) => handleFormChange('contentType', e.target.value)}
                    required
                  >
                    <option value="">Select content type</option>
                    <option value="Blog">Blog</option>
                    <option value="Story">Story</option>
                    <option value="Article">Article</option>
                    <option value="Poetry">Poetry</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Genre</label>
                  <select
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.genre}
                    onChange={(e) => handleFormChange('genre', e.target.value)}
                    required
                  >
                    <option value="">Select genre</option>
                    <option value="Romance">Romance</option>
                    <option value="Fantasy">Fantasy</option>
                    <option value="Mystery">Mystery</option>
                    <option value="Science Fiction">Science Fiction</option>
                    <option value="Horror">Horror</option>
                    <option value="Travel">Travel</option>
                    <option value="Food">Food</option>
                    <option value="Technology">Technology</option>
                  </select>
                </div>
              </div>

              {/* Deadline and Word Count */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Deadline</label>
                  <input
                    type="date"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.deadline}
                    onChange={(e) => handleFormChange('deadline', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Word Count</label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="e.g., 500-1000 words"
                    value={formData.wordCount}
                    onChange={(e) => handleFormChange('wordCount', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Language and Tags */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Language</label>
                  <select
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    required
                  >
                    <option value="">Select language</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Tags</label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="Add relevant tags (comma separated)"
                    value={formData.tags}
                    onChange={(e) => handleFormChange('tags', e.target.value)}
                  />
                </div>
              </div>

              {/* Submission Guidelines */}
              <div>
                <label className="block text-sm font-semibold text-slate-700 mb-2">Submission Guidelines</label>
                <div className="text-sm text-slate-500 mb-2">Describe the specific qualities, themes, or styles you expect in submitted drafts</div>
                <textarea
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                  placeholder="Outline what you're looking for in a submission — tone, themes, structure, or any dos and don'ts."
                  value={formData.guidelines}
                  onChange={(e) => handleFormChange('guidelines', e.target.value)}
                  rows="4"
                  required
                />
                <div className="mt-3 p-3 bg-blue-50 border-l-4 border-blue-500 rounded-lg">
                  <em className="text-sm text-slate-600">
                    Tip: Clearly define your content requirements, themes or guidelines to help authors submit work that aligns with your publication's needs.
                  </em>
                </div>
              </div>

              {/* Checkbox Options */}
              <div>
                <div className="space-y-4">
                  <label className="flex items-center gap-3 p-4 bg-blue-50/50 border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.requireFirstRights}
                      onChange={(e) => handleFormChange('requireFirstRights', e.target.checked)}
                      className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="font-medium text-slate-700">Require first Publication Rights</span>
                  </label>

                  <label className="flex items-center gap-3 p-4 bg-blue-50/50 border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.showSubmissionCount}
                      onChange={(e) => handleFormChange('showSubmissionCount', e.target.checked)}
                      className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="font-medium text-slate-700">Show number of Submissions</span>
                  </label>

                  <label className="flex items-center gap-3 p-4 bg-blue-50/50 border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.autoCloseDeadline}
                      onChange={(e) => handleFormChange('autoCloseDeadline', e.target.checked)}
                      className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="font-medium text-slate-700">Auto-Close after Deadline</span>
                  </label>
                </div>
              </div>

              {/* Make Guidelines Live Section */}
              <div className="mt-8 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl text-center">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                  Make Your Guidelines Live
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  Everything's ready! Publish your submission guidelines to start receiving high-quality drafts from talented writers.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6 border-t border-blue-100">
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-gray-400/30 bg-transparent text-gray-500 rounded-lg font-semibold hover:border-gray-500 hover:text-gray-700 transform hover:-translate-y-1 transition-all duration-300"
                >
                  Save As Draft
                </button>
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-blue-500 bg-transparent text-blue-500 rounded-lg font-semibold hover:bg-blue-50 transform hover:-translate-y-1 transition-all duration-300"
                >
                  Preview
                </button>
                <button
                  type="submit"
                  className="px-8 py-3.5 bg-gradient-to-r from-blue-500 to-blue-700 text-white border-none rounded-xl font-semibold cursor-pointer transition-all duration-300 shadow-lg hover:transform hover:-translate-y-1 hover:shadow-xl relative overflow-hidden shimmer-effect"
                >
                  <span className="relative z-10">Set Live</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Submissions Details Modal */}
      {showDetailsModal && selectedPitch && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-200 w-full max-w-6xl max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {selectedPitch.title}
                </h2>
                <p className="text-gray-600 mt-1">Submissions Overview</p>
              </div>
              <button
                onClick={closeDetailsModal}
                className="p-2 hover:bg-blue-100 rounded-lg transition-colors duration-200"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-slate-600">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {/* Pitch Info */}
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch.genre}</div>
                    <div className="text-sm text-gray-600">Genre</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch.type}</div>
                    <div className="text-sm text-gray-600">Type</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch.payout}</div>
                    <div className="text-sm text-gray-600">Payout</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{sampleSubmissions.length}</div>
                    <div className="text-sm text-gray-600">Total Submissions</div>
                  </div>
                </div>
              </div>

              {/* Submissions Table */}
              <div className="bg-white/95 backdrop-blur-xl rounded-2xl border border-blue-200 shadow-blue-100 overflow-hidden">
                {/* Table Header */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
                  <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                    <div className="col-span-3">Author</div>
                    <div className="col-span-3">Title</div>
                    <div className="col-span-2">Submitted On</div>
                    <div className="col-span-2">Status</div>
                    <div className="col-span-2">Action</div>
                  </div>
                </div>

                {/* Table Body */}
                <div className="divide-y divide-gray-100">
                  {sampleSubmissions.map((submission, index) => (
                    <div
                      key={submission.id}
                      className="px-6 py-4 hover:bg-blue-50/50 transition-all duration-300"
                    >
                      <div className="grid grid-cols-12 gap-4 items-center">
                        {/* Author */}
                        <div className="col-span-3 flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-blue-200 flex-shrink-0">
                            <img
                              src={submission.avatar}
                              alt="Author"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-800">{submission.author}</p>
                          </div>
                        </div>

                        {/* Title */}
                        <div className="col-span-3">
                          <p className="font-medium text-gray-800 truncate">{submission.title}</p>
                        </div>

                        {/* Submitted On */}
                        <div className="col-span-2">
                          <span className="text-gray-700">{submission.submittedOn}</span>
                        </div>

                        {/* Status */}
                        <div className="col-span-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                            submission.status === 'Accepted'
                              ? 'bg-green-100 text-green-700'
                              : submission.status === 'Pending'
                              ? 'bg-yellow-100 text-yellow-700'
                              : submission.status === 'Rejected'
                              ? 'bg-red-100 text-red-700'
                              : 'bg-blue-100 text-blue-700'
                          }`}>
                            {submission.status}
                          </span>
                        </div>

                        {/* Action */}
                        <div className="col-span-2">
                          <button
                            onClick={() => handleViewStory(submission)}
                            className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-0.5"
                          >
                            View
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Story Modal */}
      {showStoryModal && selectedSubmission && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-200 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            {/* Story Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center gap-4">
                <img
                  src={selectedSubmission.avatar}
                  alt="Author"
                  className="w-12 h-12 rounded-full border-2 border-blue-200"
                />
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{selectedSubmission.title}</h2>
                  <p className="text-blue-600">by {selectedSubmission.author}</p>
                </div>
              </div>
              <button
                onClick={closeStoryModal}
                className="p-2 hover:bg-blue-100 rounded-lg transition-colors duration-200"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-slate-600">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            {/* Story Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="bg-gradient-to-br from-blue-50/50 to-purple-50/50 rounded-2xl p-8">
                <div className="prose prose-lg max-w-none">
                  <div className="text-gray-800 leading-relaxed whitespace-pre-line">
                    {selectedSubmission.story}
                  </div>
                </div>
              </div>

              {/* Story Metadata */}
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white/80 rounded-xl p-4 text-center border border-blue-100">
                  <div className="text-sm text-gray-600">Content Type</div>
                  <div className="font-semibold text-blue-600">Short Story</div>
                </div>
                <div className="bg-white/80 rounded-xl p-4 text-center border border-blue-100">
                  <div className="text-sm text-gray-600">Language</div>
                  <div className="font-semibold text-blue-600">English</div>
                </div>
                <div className="bg-white/80 rounded-xl p-4 text-center border border-blue-100">
                  <div className="text-sm text-gray-600">Word Count</div>
                  <div className="font-semibold text-blue-600">{selectedSubmission.story.split(' ').length} Words</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthorPitch;
